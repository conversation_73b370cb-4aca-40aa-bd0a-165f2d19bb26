package com.rutong.medical.admin.vo.defence;

import lombok.Data;

import java.util.List;

/**
 * 防区详情VO - 包含基本信息和设备列表
 * <AUTHOR>
 * @Date 2025-07-30
 */
@Data
public class DefenceDetailVO {

    /**
     * 防区基本信息
     */
    private DefenceBasicInfo basicInfo;

    /**
     * 关联的设备列表
     */
    private List<DefenceDeviceInfo> deviceList;

    /**
     * 防区基本信息
     */
    @Data
    public static class DefenceBasicInfo {
        /**
         * 防区ID
         */
        private Long invadeDefenceId;

        /**
         * 防区编号
         */
        private String defenceCode;

        /**
         * 防区名称
         */
        private String defenceName;
    }

    /**
     * 设备信息
     */
    @Data
    public static class DefenceDeviceInfo {
        /**
         * 设备ID
         */
        private Long deviceId;

        /**
         * 设备名称
         */
        private String deviceName;

        /**
         * 终端SN
         */
        private String deviceSn;

        /**
         * 设备位置
         */
        private String spaceFullName;
    }
}
